const glob = require('glob')
const path = require('path')
const webpack = require('webpack')
const merge = require('webpack-merge');
const { VueLoaderPlugin } = require('vue-loader')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CleanWebpackPlugin = require('clean-webpack-plugin')
const fs = require('fs');

const elpisPageEntries = {};
const elpisHtmlWebpackPluginList = [];
// 获取 elpis/app/pages 目录下的所有页面入口文件(entry.xx.js)
const elpisEntryList = path.resolve(__dirname, '../../pages/**/entry.*.js');
glob.sync(elpisEntryList).forEach(file => {
  handleFile(file, elpisPageEntries, elpisHtmlWebpackPluginList)
})

const buinessPageEntries = {};
const buinessHtmlWebpackPluginList = [];
// 获取 app/pages 目录下的所有页面入口文件(entry.xx.js)
const buinessEntryList = path.resolve(process.cwd(), './app/pages/**/entry.*.js');
glob.sync(buinessEntryList).forEach(file => {
  handleFile(file, buinessPageEntries, buinessHtmlWebpackPluginList)
})

// 构造相关 webpack 处理的数据机构
function handleFile(file, entries = {}, htmlWebpackPluginList = []) {
  const entryName = path.basename(file, '.js');
  // 构造 entry
  entries[entryName] = file;
  // 构造最终渲染的页面文件
  htmlWebpackPluginList.push(new HtmlWebpackPlugin({
    // 产物最终模版
    filename: path.resolve(process.cwd(), './app/public/dist/', `${entryName}.tpl`),
    // 指定模版文件
    template: path.resolve(__dirname, '../../view/entry.tpl'),
    // 要注入的代码块
    chunks: [entryName]
  }))
}

// 加载 业务 webpack 配置
let buinessWebpackConfig = {};
try {
  buinessWebpackConfig = require(`${process.cwd()}/app/webpack.config.js`);
} catch { }


/**
 * webpack 基础配置
 */
module.exports = merge.smart({
  // 入口配置
  entry: Object.assign({}, elpisPageEntries, buinessPageEntries),
  // 模块解析配置
  module: {
    rules: [{
      test: /\.vue$/,
      use: {
        loader: require.resolve('vue-loader')
      }
    },
    {
      test: /\.js$/,
      include: [
        // 处理 elpis 代码进行babel,加快打包速度
        path.resolve(__dirname, '../../pages'),
        // 处理业务代码进行babel,加快打包速度
        path.resolve(process.cwd(), '../../pages')
      ],
      use: {
        loader: require.resolve('babel-loader')
      }
    },
    {
      test: /\.(png|jpe?g|gif)(\?.+)?$/,
      use: {
        loader: require.resolve('url-loader'),
        options: {
          limit: 300, // 小于300kb的图片会被转成base64编码
          esMoule: false // 禁用esModule
        }
      }
    }, {
      test: /\.css$/,
      use: [require.resolve('style-loader'), require.resolve('css-loader')]
    }, {
      test: /\.less$/,
      use: [require.resolve('style-loader'), require.resolve('css-loader'), require.resolve('less-loader')]
    },
    {
      test: /\.(eot|svg|ttf|woff|woff2)(\?\S*)?$/, // 例如:file.woff2?v=abc&opt=1
      use: require.resolve('file-loader')
    }
    ]
  },
  // 产物输出路径, 因为开发和生产环境输出不一致,所以在各自环境中进行配置
  output: {},
  // 配置模块解析的具体行为(定义 webpack 在打包时,如何找到并解析具体模块的路径)
  resolve: {
    // 尝试按顺序解析这些后缀名。如果有多个文件有相同的名字，但后缀名不同，webpack 会解析列在数组首位的后缀的文件 并跳过其余的后缀
    // 能够使用户在引入模块时不带扩展：import File from '../path/to/file';
    extensions: ['.js', '.vue', '.less', '.css'],
    // 配置别名: import { xxx } from '$elpisCommon/xxx';
    alias: (() => {
      const aliasMap = {};
      const blankModulePath = path.resolve(__dirname, '../libs/blank.js');
      // dashboard 路由扩展
      const businessDashboardRouterConfigPath = path.resolve(process.cwd(), './app/pages/dashboard/router.js');
      aliasMap['$businessDashboardRouterConfig'] = fs.existsSync(businessDashboardRouterConfigPath) ? businessDashboardRouterConfigPath : blankModulePath;

      // schema-view component 扩展
      const businessComponentConfigPath = path.resolve(process.cwd(), './app/pages/dashboard/complex-view/schema-view/components/component-config.js');
      aliasMap['$businessComponentConfig'] = fs.existsSync(businessComponentConfigPath) ? businessComponentConfigPath : blankModulePath;

      // schema-from 扩展
      const businessFromComponentConfigPath = path.resolve(process.cwd(), './app/pages/widgets/schema-form/form-item-config.js');
      aliasMap['$businessFormItemConfig'] = fs.existsSync(businessFromComponentConfigPath) ? businessFromComponentConfigPath : blankModulePath;

      // search-bar 扩展
      const businessSearchComponentConfigPath = path.resolve(process.cwd(), './app/pages/widgets/schema-search-bar/search-item-config.js');
      aliasMap['$businessSearchItemConfig'] = fs.existsSync(businessSearchComponentConfigPath) ? businessSearchComponentConfigPath : blankModulePath;

      return {
        'vue': require.resolve('vue'),
        '@babel/runtime/helpers/asyncToGenerator': require.resolve('@babel/runtime/helpers/asyncToGenerator'),
        '@babel/runtime/regenerator': require.resolve('@babel/runtime/regenerator'),
        $elpisPages: path.resolve(__dirname, '../../pages'), // 定义别名,方便引入业务代码
        $elpisCommon: path.resolve(__dirname, '../../pages/common'),
        $elpisCurl: path.resolve(__dirname, '../../pages/common/curl.js'),
        $elpisUntil: path.resolve(__dirname, '../../pages/common/untils.js'),
        $elpisWidgets: path.resolve(__dirname, '../../pages/widgets'),
        $elpisHeaderContainer: path.resolve(__dirname, '../../pages/widgets/header-container/header-container.vue'),
        $elpisSiderContainer: path.resolve(__dirname, '../../pages/widgets/sider-container/sider-container.vue'),
        $elpisSchemaTable: path.resolve(__dirname, '../../pages/widgets/schema-table/schema-table.vue'),
        $elpisSchemaForm: path.resolve(__dirname, '../../pages/widgets/schema-form/schema-form.vue'),
        $elpisSchemaSearchBar: path.resolve(__dirname, '../../pages/widgets/scheam-search-bar/scheam-search-bar.vue'),
        $elpisStore: path.resolve(__dirname, '../../pages/store'),
        $elpisBoot: path.resolve(__dirname, '../../pages/boot.js'),
        ...aliasMap
      }
    })(),
  },
  // 配置 webpack 插件
  plugins: [
    // 处理 .vue 文件,这个插件是必须de
    // 它的职能是将定义过的其他规则复制并应用到 .vue 文件中
    // 例如,如果有一条匹配规则 /\.js$/ 的规则, 那么他会应用到 .vue 文件中的 script 板块中
    new VueLoaderPlugin(),
    // 把第三方库暴露到 window context 下 
    // 任何文件都可以直接使用 Vue，Webpack 会自动将其映射为 require('vue')。
    // 例如 new Vue( { el: '#app', render: h => h(App) } );
    new webpack.ProvidePlugin({
      Vue: 'vue',
      axios: 'axios',
      _: 'lodash'
    }),
    // 定义全局常量
    new webpack.DefinePlugin({
      __VUE_OPRIONS_API__: 'true', // 禁用选项式 API 支持
      __VUE_PRO_DEVTOOLS: 'false', // 禁用 vue 调试工具
      __VUE_PRO_HYDRATION_MISMATCH_DETAILS__: 'false' // 禁用生产环境构建下激活 (hydration) 不匹配的详细警告
    }),
    // 显示打包进度
    new webpack.ProgressPlugin(),
    // 每次 build 前清空 public/dist 目录
    new CleanWebpackPlugin(['public/dist'], {
      root: path.resolve(process.cwd(), './app/'),
      exclude: [],
      verbose: true,
      dry: false
    }),
    // 构造最终渲染的页面模版
    ...elpisHtmlWebpackPluginList,
    ...buinessHtmlWebpackPluginList
  ],
  // 配置打包输出优化(代码分割,模块分割,缓存,treeShaing,压缩等优化策略)
  optimization: {
    /**
     * 把 js 文件打包成3种类型
     * 1. verdor: 第三方 lib 库, 基本不会改动, 除非依赖版本升级
     * 2. common: 业务组件代码的公共部分抽取出来, 改动较少
     * 3. ebnty.{page}:  不同页面 entry 里的业务组件代码的差异部分,会经常改动
     * 目的: 把改动和引用频率不一样的 js 区分出来,已达到更好利用浏览器缓存的效果
     */
    splitChunks: {
      chunks: 'all', // 对同步和异步模块都进行分割
      maxAsyncRequests: 10, // 每次异步加载的最大并行请求数
      maxInitialRequests: 10, // 入口点的最大并行请求数
      cacheGroups: {
        vendor: { // 第三方库
          test: /[\\/]node_modules[\\/]/, // 打包node_modules 目录下的模块
          name: 'vendor', //模块名称
          priority: 20, // 优先级,数字越大越优先
          enforce: true, // 强制执行
          reuseExistingChunk: true, // 复用已有的公共 chunk
        },
        common: { // 业务组件公共代码
          test: /[\\/]common|widgets[\\/]/,
          name: 'common',
          minChunks: 2, // 被两处引用即被归为公共模块
          minSize: 1, // 最小分割文件大小 
          priority: 10, // 优先级
          reuseExistingChunk: true, // 复用已有的公共 chunk
        }
      },
    },
    // 将 webpack 运行时生成的代码打包到 runtime.js
    runtimeChunk: true
  },
}, buinessWebpackConfig)
